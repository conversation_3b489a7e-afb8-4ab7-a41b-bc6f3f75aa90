"""
Celery Application Configuration for PlanBookAI
Cấ<PERSON> chuẩ<PERSON> chỉnh với Redis broker và auto-discovery tasks
"""

import os
import sys
import traceback
import logging
from celery import Celery
from celery.signals import task_failure, task_retry, worker_ready
from kombu import Queue

# Setup logging
logger = logging.getLogger(__name__)

# Add project root to Python path for imports
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.core.config import settings

# Custom exception handler
class CeleryExceptionHandler:
    """Custom exception handler for Celery tasks"""
    
    @staticmethod
    def format_exception(exc, task_id=None, task_name=None):
        """Format exception for proper serialization"""
        try:
            return {
                'exc_type': exc.__class__.__name__,
                'exc_message': str(exc),
                'exc_module': exc.__class__.__module__,
                'task_id': task_id,
                'task_name': task_name,
                'traceback': traceback.format_exc()
            }
        except Exception as e:
            return {
                'exc_type': 'UnknownException',
                'exc_message': f'Failed to format exception: {str(e)}',
                'task_id': task_id,
                'task_name': task_name,
                'traceback': 'N/A'
            }

# Tạo Celery instance
celery_app = Celery("planbook_ai")

# Signal handlers
@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwargs):
    """Handle task failure and log properly"""
    task_name = sender.name if sender and hasattr(sender, 'name') else 'unknown'
    logger.error(
        f"Task {task_name}[{task_id}] failed: {exception}",
        extra={
            'task_id': task_id,
            'task_name': task_name,
            'exception_type': type(exception).__name__ if exception else 'Unknown',
            'exception_message': str(exception) if exception else 'No message',
            'traceback': str(traceback) if traceback else 'N/A'
        }
    )

@task_retry.connect
def task_retry_handler(sender=None, task_id=None, reason=None, einfo=None, **kwargs):
    """Handle task retry"""
    task_name = sender.name if sender and hasattr(sender, 'name') else 'unknown'
    logger.warning(
        f"Task {task_name}[{task_id}] retry: {reason}",
        extra={
            'task_id': task_id,
            'task_name': task_name,
            'retry_reason': str(reason) if reason else 'No reason'
        }
    )

@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Handle worker ready signal"""
    hostname = sender.hostname if sender and hasattr(sender, 'hostname') else 'unknown'
    logger.info(f"Celery worker {hostname} is ready")

# Cấu hình Celery từ settings
celery_app.conf.update(
    # Broker và Result Backend
    broker_url=settings.CELERY_BROKER_URL,
    result_backend=settings.CELERY_RESULT_BACKEND,
    # Serialization
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,
    result_accept_content=settings.CELERY_ACCEPT_CONTENT,
    # Exception handling
    task_track_started=True,
    task_publish_retry=True,
    # Timezone
    timezone=settings.CELERY_TIMEZONE,
    enable_utc=settings.CELERY_ENABLE_UTC,
    # Task routing và queues
    task_routes={
        "app.tasks.pdf_tasks.*": {"queue": "pdf_queue"},
        "app.tasks.embeddings_tasks.*": {"queue": "embeddings_queue"},
        "app.tasks.cv_tasks.*": {"queue": "cv_queue"},
    },
    # Định nghĩa queues
    task_queues=(
        Queue("default", routing_key="default"),
        Queue("pdf_queue", routing_key="pdf_queue"),
        Queue("embeddings_queue", routing_key="embeddings_queue"),
        Queue("cv_queue", routing_key="cv_queue"),
    ),
    # Task execution settings
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_reject_on_worker_lost=True,
    # Exception serialization fix
    task_always_eager=False,
    task_eager_propagates=True,
    task_store_eager_result=True,
    # Result settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    # Task time limits
    task_soft_time_limit=1800,  # 30 minutes
    task_time_limit=2400,  # 40 minutes
    # Worker settings
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=True,
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    # Include tasks - auto-discovery
    include=[
        "app.tasks.pdf_tasks",
        "app.tasks.embeddings_tasks",
        "app.tasks.cv_tasks",
    ],
)

# Auto-discover tasks từ installed apps
celery_app.autodiscover_tasks(
    [
        "app.tasks",
    ]
)


# Task decorator với default settings và exception handling
def task_with_retry(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
):
    """Decorator cho tasks với retry logic và exception handling"""

    def decorator(func):
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as exc:
                # Format exception properly for serialization
                formatted_exc = CeleryExceptionHandler.format_exception(
                    exc, 
                    task_id=self.request.id if self else None,
                    task_name=func.__name__
                )
                logger.error(f"Task {func.__name__} failed: {formatted_exc}")
                raise exc
        
        return celery_app.task(
            bind=bind, autoretry_for=autoretry_for, retry_kwargs=retry_kwargs
        )(wrapper)

    return decorator


# Health check task
@celery_app.task(name="app.tasks.health_check")
def health_check():
    """Health check task để test Celery worker"""
    return {
        "status": "healthy",
        "message": "Celery worker is running",
        "broker": settings.CELERY_BROKER_URL,
        "backend": settings.CELERY_RESULT_BACKEND,
    }


# Task để test connection
@celery_app.task(name="app.tasks.test_task")
def test_task(message: str = "Hello from Celery!"):
    """Simple test task"""
    return {
        "success": True,
        "message": message,
        "worker_info": {
            "broker": celery_app.conf.broker_url,
            "backend": celery_app.conf.result_backend,
        },
    }


if __name__ == "__main__":
    celery_app.start()
